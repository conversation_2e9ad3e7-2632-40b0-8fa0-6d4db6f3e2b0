import { Thread } from "@langchain/langgraph-sdk";
import { useMemo } from "react";

// 线程元数据接口
export interface ThreadMetadata {
  id: string;
  title: string;
  lastActivity: string;
  taskCount: number;
  repository: string;
  branch: string;
  status: string;
  githubIssue?: {
    number: number;
    url: string;
  };
}

// 线程状态错误接口
export interface ThreadStatusError {
  message: string;
  type: string;
}

// 计算线程标题
function computeThreadTitle(thread: Thread): string {
  // 如果有自定义标题，使用自定义标题
  if (thread.metadata?.title) {
    return thread.metadata.title;
  }
  
  // 如果有消息，使用第一条用户消息作为标题
  if (thread.values && (thread.values as any).messages) {
    const messages = (thread.values as any).messages;
    const firstUserMessage = messages.find((msg: any) => msg.type === 'human');
    if (firstUserMessage && firstUserMessage.content) {
      const content = typeof firstUserMessage.content === 'string' 
        ? firstUserMessage.content 
        : JSON.stringify(firstUserMessage.content);
      return content.slice(0, 50) + (content.length > 50 ? '...' : '');
    }
  }
  
  // 默认标题
  return `聊天 - ${thread.thread_id.slice(0, 8)}`;
}

// 计算最后活动时间
function calculateLastActivity(updatedAt: string): string {
  const now = new Date();
  const updated = new Date(updatedAt);
  const diffMs = now.getTime() - updated.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) {
    return '刚刚';
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else {
    return `${diffDays}天前`;
  }
}

/**
 * Hook that combines thread metadata with real-time status
 */
export function useThreadMetadata(thread: Thread): {
  metadata: ThreadMetadata;
  isStatusLoading: boolean;
  statusError: Error | ThreadStatusError | null;
} {
  // 简化版本，不进行实时状态查询
  const isStatusLoading = false;
  const statusError = null;

  const metadata: ThreadMetadata = useMemo((): ThreadMetadata => {
    const values = thread.values || {};

    return {
      id: thread.thread_id,
      title: computeThreadTitle(thread),
      lastActivity: calculateLastActivity(thread.updated_at),
      taskCount: 0, // 简化版本，暂时设为0
      repository: thread.metadata?.graph_id || "",
      branch: "main",
      status: thread.status || "idle",
      githubIssue: undefined, // 简化版本，暂时不支持GitHub issue
    };
  }, [thread]);

  return {
    metadata,
    isStatusLoading,
    statusError,
  };
}
