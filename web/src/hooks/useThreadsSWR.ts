import useS<PERSON> from "swr";
import { Thread } from "@langchain/langgraph-sdk";
import { createClient, DEFAULT_API_URL } from "@/providers/client";
import { THREAD_SWR_CONFIG, DEFAULT_PAGINATION } from "@/lib/swr-config";
import { 
  AnyGraphState, 
  PaginationOptions, 
  ThreadSortBy, 
  SortOrder,
  ExtendedThread 
} from "@/types/thread";
import { useMemo, useState } from "react";

/**
 * useThreadsSWR选项接口
 */
interface UseThreadsSWROptions {
  /**
   * 助手ID（图ID），用于过滤特定图的线程
   */
  assistantId?: string;
  /**
   * 刷新间隔（毫秒）
   */
  refreshInterval?: number;
  /**
   * 窗口聚焦时重新验证
   */
  revalidateOnFocus?: boolean;
  /**
   * 重新连接时重新验证
   */
  revalidateOnReconnect?: boolean;
  /**
   * 当前用户ID，用于过滤用户的线程
   */
  currentUserId?: string | null;
  /**
   * 禁用用户过滤
   */
  disableUserFiltering?: boolean;
  /**
   * 分页选项
   */
  pagination?: PaginationOptions;
}

/**
 * 线程搜索参数
 */
interface ThreadSearchArgs {
  metadata?: {
    graph_id?: string;
    user_id?: string;
  };
  limit?: number;
  offset?: number;
  sortBy?: ThreadSortBy;
  sortOrder?: SortOrder;
}

/**
 * Hook for fetching threads for any graph type.
 * 支持所有图状态类型（DesignToCode, SimpleChat等）
 * 通过传递相应的assistantId来工作。
 *
 * @param options 配置选项
 * @returns 线程数据和相关状态
 */
export function useThreadsSWR<
  TGraphState extends AnyGraphState = AnyGraphState,
>(options: UseThreadsSWROptions = {}) {
  const {
    assistantId,
    refreshInterval = THREAD_SWR_CONFIG.refreshInterval,
    revalidateOnFocus = THREAD_SWR_CONFIG.revalidateOnFocus,
    revalidateOnReconnect = THREAD_SWR_CONFIG.revalidateOnReconnect,
    currentUserId,
    disableUserFiltering = false,
    pagination,
  } = options;

  const [hasMoreState, setHasMoreState] = useState(true);

  // 合并默认分页配置
  const paginationWithDefaults = {
    ...DEFAULT_PAGINATION,
    ...pagination,
  };

  // 为SWR缓存创建唯一键，基于assistantId和分页参数
  const swrKey = useMemo(() => {
    const baseKey = assistantId ? ["threads", assistantId] : ["threads", "all"];
    if (pagination || currentUserId) {
      return [
        ...baseKey,
        paginationWithDefaults.limit,
        paginationWithDefaults.offset,
        paginationWithDefaults.sortBy,
        paginationWithDefaults.sortOrder,
        currentUserId || "no-user",
      ];
    }
    return baseKey;
  }, [assistantId, paginationWithDefaults, currentUserId]);

  // 数据获取器
  const fetcher = async (): Promise<Thread<TGraphState>[]> => {
    const client = createClient(DEFAULT_API_URL);
    
    // 构建搜索参数
    const searchArgs: ThreadSearchArgs = {
      ...paginationWithDefaults,
    };

    // 添加元数据过滤
    if (assistantId || currentUserId) {
      searchArgs.metadata = {};
      if (assistantId) {
        searchArgs.metadata.graph_id = assistantId;
      }
      if (currentUserId && !disableUserFiltering) {
        searchArgs.metadata.user_id = currentUserId;
      }
    }

    try {
      return await client.threads.search<TGraphState>(searchArgs);
    } catch (error) {
      console.error("Failed to fetch threads:", error);
      throw error;
    }
  };

  // 使用SWR进行数据获取
  const { data, error, isLoading, mutate, isValidating } = useSWR(
    swrKey,
    fetcher,
    {
      refreshInterval,
      revalidateOnFocus,
      revalidateOnReconnect,
      errorRetryCount: THREAD_SWR_CONFIG.errorRetryCount,
      errorRetryInterval: THREAD_SWR_CONFIG.errorRetryInterval,
      dedupingInterval: THREAD_SWR_CONFIG.dedupingInterval,
    },
  );

  // 处理和过滤线程数据
  const threads = useMemo(() => {
    const allThreads = data ?? [];

    if (disableUserFiltering) {
      return allThreads;
    }

    if (!allThreads.length) {
      setHasMoreState(false);
      return [];
    }

    if (!currentUserId) {
      // 如果没有当前用户ID，返回所有线程
      return allThreads;
    }

    // 按用户ID过滤线程
    return allThreads.filter((thread) => {
      const threadUserId = thread.metadata?.user_id;
      return (
        typeof threadUserId === "string" &&
        threadUserId === currentUserId
      );
    });
  }, [data, currentUserId, disableUserFiltering]);

  // 计算是否还有更多数据
  const hasMore = useMemo(() => {
    return hasMoreState && !!threads.length;
  }, [threads.length, hasMoreState]);

  return {
    threads,
    error,
    isLoading,
    isValidating,
    mutate,
    hasMore,
  };
}
